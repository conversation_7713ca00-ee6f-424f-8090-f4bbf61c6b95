/* CcInput组件样式 */
.cc-input-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.cc-count {
  color: #999;
  font-size: 12px;
  text-align: right;
  white-space: nowrap;
  min-width: 40px;
}

/* 错误提示区域样式 */
.cc-error-message {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, opacity 0.3s ease;
  opacity: 0;
  line-height: 1.4;
}

.cc-error-message.show {
  max-height: 50px;
  opacity: 1;
  padding: 4px 0;
}

/* 输入框抖动动画 */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }
  20%, 40%, 60%, 80% { transform: translateX(3px); }
}

.cc-input-shake {
  animation: shake 0.6s ease-in-out;
  border-color: #ff4d4f !important;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2) !important;
}

/* 已选用户标签区域 */
.cc-selected-users {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 4px;
  padding: 0;
  align-items: flex-start;
  align-content: flex-start;
}

.cc-selected-users .ant-tag {
  margin: 0;
  cursor: default;
  user-select: none;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.cc-selected-users .ant-tag .ant-tag-close-icon {
  margin-left: 4px;
  cursor: pointer;
}

.cc-selected-users .ant-tag .ant-tag-close-icon:hover {
  color: #ff4d4f;
}

/* 输入框容器样式 */
.cc-input-container .ant-input:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cc-selected-users .ant-tag {
    max-width: 150px;
    font-size: 12px;
  }
}
