import styled from 'styled-components';
import { Form } from 'antd';

export const SearchFormContainer = styled.div`
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;

  .ant-form-inline .ant-form-item {
    margin-bottom: 16px; // 调整表单项之间的垂直间距
    margin-right: 16px; // 调整表单项之间的水平间距
    display: inline-flex;
    align-items: flex-start; // 顶部对齐
  }

  .ant-form-inline {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start; // 确保所有表单项顶部对齐
  }

  .ant-form-item-label {
    white-space: nowrap; // 防止标签换行
  }
`;

export const StyledFormItem = styled(Form.Item)`
  // 迭代二：已填写/未填写状态的视觉区分
  &.has-value .ant-input-affix-wrapper,
  &.has-value .ant-picker {
    border-color: #1890ff; // 有值时边框变蓝
    transition: border-color 0.3s ease; // 添加平滑过渡
  }
  &.no-value .ant-input-affix-wrapper,
  &.no-value .ant-picker {
    border-color: #d9d9d9; // 无值时边框恢复默认
    transition: border-color 0.3s ease; // 添加平滑过渡
  }

  // 防止浏览器自动填充样式干扰
  .ant-input:-webkit-autofill,
  .ant-input:-webkit-autofill:hover,
  .ant-input:-webkit-autofill:focus {
    -webkit-box-shadow: 0 0 0 1000px white inset !important;
    -webkit-text-fill-color: #000000 !important;
    transition: background-color 5000s ease-in-out 0s;
  }

  // 确保输入框内部没有异常背景
  .ant-input-affix-wrapper .ant-input {
    background-color: transparent !important;
  }

  // 优化focus状态，避免与has-value样式冲突
  &.has-value .ant-input-affix-wrapper:focus,
  &.has-value .ant-input-affix-wrapper-focused {
    border-color: #40a9ff !important; // focus时使用稍浅的蓝色
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
  }

  &.has-value .ant-input-affix-wrapper:hover {
    border-color: #40a9ff !important; // hover时使用稍浅的蓝色
  }

  // 确保表单项垂直对齐
  .ant-form-item-control {
    position: relative;
  }

  // 日期范围选择器的特殊样式
  &.date-range-item {
    min-width: 280px; // 确保日期范围选择器有足够宽度
  }
  
  // 独立日期选择器的样式
  &.date-item {
    min-width: 200px; // 为独立日期选择器设置合适宽度
  }

  // 错误状态下的样式
  &.ant-form-item-has-error .ant-input-affix-wrapper,
  &.ant-form-item-has-error .ant-picker {
    border-color: #ff4d4f !important;
  }
  
  // 错误状态下的hover效果
  &.ant-form-item-has-error .ant-input-affix-wrapper:hover,
  &.ant-form-item-has-error .ant-picker:hover {
    border-color: #ff4d4f !important;
    box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
  }
  
  // 错误状态下的focus效果
  &.ant-form-item-has-error .ant-input-affix-wrapper:focus,
  &.ant-form-item-has-error .ant-input-affix-wrapper-focused,
  &.ant-form-item-has-error .ant-picker:focus,
  &.ant-form-item-has-error .ant-picker-focused {
    border-color: #ff4d4f !important;
    box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
  }
`;

export const DateRangeErrorTip = styled.div`
  color: #ff4d4f; /* Ant Design 错误提示的红色 */
  font-size: 12px;
  margin-top: 4px;
`;
