import React, { Component } from 'react';
import { Input, Button, Form, DatePicker } from 'antd';
import { SearchFormContainer, StyledFormItem } from './styles';
import { trimAndHandleEmpty } from '../../util/strHelper';
import { isValidDateRange } from '../../util/timeHelper';
import dayjs from 'dayjs';

class SearchForm extends Component {
  constructor(props) {
    super(props);
    this.state = {
      orderId: props.initialSearchParams?.orderId || '',
      title: props.initialSearchParams?.title || '',
      // 迭代二：日期范围相关
      startDate: props.initialSearchParams?.appliedStartDate ? dayjs(props.initialSearchParams.appliedStartDate) : null,
      endDate: props.initialSearchParams?.appliedEndDate ? dayjs(props.initialSearchParams.appliedEndDate) : null,
      dateRangeError: '', // 日期范围校验错误信息
      orderIdError: '', // 单号校验错误信息
      titleError: '', // 标题校验错误信息
      // 迭代三占位：工单类型相关
      orderTypeFilterVisible: false,
      selectedOrderTypes: [],
    };
    this.handleInputChange = this.handleInputChange.bind(this);
    this.handleDateRangeChange = this.handleDateRangeChange.bind(this);
    this.handleSearchClick = this.handleSearchClick.bind(this);
    this.validateDateRange = this.validateDateRange.bind(this);
  }

  componentDidUpdate(prevProps) {
    // 检查 props 中的搜索参数是否发生变化，如果变化则更新内部 state
    const { initialSearchParams } = this.props;
    if (initialSearchParams && prevProps.initialSearchParams &&
        (initialSearchParams.orderId !== prevProps.initialSearchParams.orderId ||
         initialSearchParams.title !== prevProps.initialSearchParams.title ||
         initialSearchParams.appliedStartDate !== prevProps.initialSearchParams.appliedStartDate ||
         initialSearchParams.appliedEndDate !== prevProps.initialSearchParams.appliedEndDate)) {
      this.setState({
        orderId: initialSearchParams.orderId || '',
        title: initialSearchParams.title || '',
        startDate: initialSearchParams.appliedStartDate ? dayjs(initialSearchParams.appliedStartDate) : null,
        endDate: initialSearchParams.appliedEndDate ? dayjs(initialSearchParams.appliedEndDate) : null,
        dateRangeError: '', // props更新时清空错误
      });
    }
  }

  handleInputChange(name, value) {
    // 实时验证输入内容
    let error = '';

    if (name === 'orderId') {
      // 验证单号不能仅包含空格
      if (value && typeof value === 'string' && value.trim() === '' && value.length > 0) {
        error = '请输入有效单号';
      }
      this.setState({ [name]: value, orderIdError: error });
    } else if (name === 'title') {
      // 验证标题不能仅包含空格
      if (value && typeof value === 'string' && value.trim() === '' && value.length > 0) {
        error = '请输入有效标题';
      }
      this.setState({ [name]: value, titleError: error });
    } else {
      this.setState({ [name]: value });
    }

    // 当输入框被清空时，立即通知父组件更新搜索条件
    if (!value || value.trim() === '') {
      this.notifySearchParamsChange();
    }
  }

  // 获取当前搜索参数（供父组件调用）
  getCurrentSearchParams = () => {
    const { orderId, title, startDate, endDate } = this.state;
    return {
      orderId: trimAndHandleEmpty(orderId),
      title: trimAndHandleEmpty(title),
      appliedStartDate: startDate ? startDate.format('YYYY-MM-DD HH:mm:ss') : null,
      appliedEndDate: endDate ? endDate.format('YYYY-MM-DD HH:mm:ss') : null,
    };
  };

  // 通知父组件搜索参数发生变化（用于实时同步清空操作）
  notifySearchParamsChange() {
    if (this.props.onSearchParamsChange) {
      this.props.onSearchParamsChange(this.getCurrentSearchParams());
    }
  }

  validateDateRange(dates) {
    const [startDate, endDate] = dates || [null, null];
    return isValidDateRange(startDate ? startDate.toDate() : null, endDate ? endDate.toDate() : null);
  }

  handleDateRangeChange(dates) {
    const [newStartDate, newEndDate] = dates || [null, null];
    let error = '';

    // 实时校验：如果两个日期都已选择，且结束日期早于开始日期
    if (newStartDate && newEndDate && newEndDate.isBefore(newStartDate)) {
      error = '结束日期不能早于开始日期';
      // 自动将无效的结束日期置为未输入状态
      this.setState({ startDate: newStartDate, endDate: null, dateRangeError: error }, () => {
        this.notifySearchParamsChange();
      });
    } else {
      this.setState({
        startDate: newStartDate,
        endDate: newEndDate,
        dateRangeError: '', // 清空错误
      }, () => {
        // 当日期被清空时，通知父组件
        if (!newStartDate && !newEndDate) {
          this.notifySearchParamsChange();
        }
      });
    }
  }

  // 处理独立日期选择器的变化
  handleSingleDateChange(type, date) {
    let newStartDate = this.state.startDate;
    let newEndDate = this.state.endDate;

    if (type === 'start') {
      newStartDate = date;
    } else {
      newEndDate = date;
    }

    let error = '';
    // 只有当两个日期都选择时才进行校验
    if (newStartDate && newEndDate && newEndDate.isBefore(newStartDate)) {
      error = '结束日期不能早于开始日期';
    }

    this.setState({
      startDate: newStartDate,
      endDate: newEndDate,
      dateRangeError: error,
    }, () => {
      // 当日期被清空时，通知父组件
      if (!date) {
        this.notifySearchParamsChange();
      }
    });
  }

  handleSearchClick() {
    const { orderId, title, startDate, endDate, dateRangeError, orderIdError, titleError } = this.state;

    // 迭代三：增强校验逻辑
    const validationErrors = [];
    
    // 检查所有校验错误
    if (dateRangeError) {
      validationErrors.push(dateRangeError);
    }
    if (orderIdError) {
      validationErrors.push(orderIdError);
    }
    if (titleError) {
      validationErrors.push(titleError);
    }
    
    // 前端处理：当单号或标题输入框中仅包含空格时，置为空字符串
    const processedOrderId = trimAndHandleEmpty(orderId);
    const processedTitle = trimAndHandleEmpty(title);
    
    // 检查单号格式（如果不为空）
    if (processedOrderId && processedOrderId.length > 50) {
      validationErrors.push('单号长度不能超过50个字符');
    }
    
    // 检查标题格式（如果不为空）
    if (processedTitle && processedTitle.length > 100) {
      validationErrors.push('标题长度不能超过100个字符');
    }
    
    // 迭代三：如果存在校验错误，触发全局提示
    if (validationErrors.length > 0) {
      if (this.props.onValidationError) {
        this.props.onValidationError(validationErrors.join('； '));
      }
      return; // 停止搜索，不传递给父组件
    }
    
    // 没有错误时才执行搜索
    // 构造搜索参数对象，传递给父组件
    this.props.onSearch({
      orderId: processedOrderId,
      title: processedTitle,
      appliedStartDate: startDate ? startDate.format('YYYY-MM-DD HH:mm:ss') : null, // 格式化为中国本地时间字符串
      appliedEndDate: endDate ? endDate.format('YYYY-MM-DD HH:mm:ss') : null,     // 格式化为中国本地时间字符串
      orderTypes: this.state.selectedOrderTypes,
    });
  }

  render() {
    const { orderId, title, startDate, endDate, dateRangeError, orderIdError, titleError } = this.state;
    return (
      <SearchFormContainer>
        <Form layout="inline">
          <StyledFormItem
            label="单号"
            // 迭代二：视觉区分：根据是否有值添加类名
            className={orderId ? 'has-value' : 'no-value'}
            validateStatus={orderIdError ? 'error' : ''}
            help={orderIdError}
          >
            <Input
              name="orderId"
              placeholder="请输入单号"
              value={orderId}
              onChange={(e) => this.handleInputChange('orderId', e.target.value)}
              allowClear
              status={orderIdError ? 'error' : ''}
            />
          </StyledFormItem>
          <StyledFormItem
            label="标题"
            // 迭代二：视觉区分
            className={title ? 'has-value' : 'no-value'}
            validateStatus={titleError ? 'error' : ''}
            help={titleError}
          >
            <Input
              name="title"
              placeholder="请输入标题"
              value={title}
              onChange={(e) => this.handleInputChange('title', e.target.value)}
              allowClear
              status={titleError ? 'error' : ''}
            />
          </StyledFormItem>

          {/* 迭代二：申请日期时间范围搜索 */}
          <StyledFormItem
            label="申请开始日期"
            className={`date-item ${startDate ? 'has-value' : 'no-value'}`}
          >
            <DatePicker
              showTime
              value={startDate}
              onChange={(date) => this.handleSingleDateChange('start', date)}
              placeholder="选择开始日期"
              style={{ width: '100%' }}
            />
          </StyledFormItem>
          
          <StyledFormItem
            label="申请结束日期"
            className={`date-item ${endDate ? 'has-value' : 'no-value'}`}
            validateStatus={dateRangeError ? 'error' : ''}
            help={dateRangeError}
          >
            <DatePicker
              showTime
              value={endDate}
              onChange={(date) => this.handleSingleDateChange('end', date)}
              placeholder="选择结束日期"
              style={{ width: '100%' }}
              status={dateRangeError ? 'error' : ''}
            />
          </StyledFormItem>

          <Form.Item>
            <Button type="primary" onClick={this.handleSearchClick}>
              搜索
            </Button>
          </Form.Item>
        </Form>
      </SearchFormContainer>
    );
  }
}

export default SearchForm;
