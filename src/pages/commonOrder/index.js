import { Component } from "react";
import { Input, Button, Select, message, Segmented, Tag, Upload } from "antd";
import {
    UserOutlined,
    InboxOutlined
} from "@ant-design/icons";
import Cookies from "js-cookie";

import styled from "styled-components";
import CryptoJS from "crypto-js"
import moment from 'moment';

import {
    requestLeaderEmail,
    requestOpsMember,
    requestOpsMemberDutySpectrum,
    requestSuperLeaderEmails,
    requestCommonOrder,
    requestTxCamCosAuth
} from "../../request/api";
import CcInput from "../../components/CcInput";
import withRouter from "@/util/withRouter";
import navigateModal from "@/util/navigateModal"
import { DoingOrderUrlSuffix } from "@/global"
import getFileContent from "@/util/fileHelper";
import { SideContent, ShadeForm, OrderTitle, OrderRow, OrderSubmit } from "@/common/styleLayout";

const { Option } = Select;
const { <PERSON><PERSON> } = Upload;

// css-js start ↓↓↓
const CommonForm = styled(ShadeForm)`
  width: 55%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
`

const AuditPersonSpan = styled.span`
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  white-space: nowrap;
`

// 自定义上传组件样式，确保与需求描述区域保持一致的宽度
const DraggerWrapper = styled.div`
  width: 100%; // 确保DraggerWrapper占据父容器的全部宽度
  display: flex;
  flex: 1; // 确保DraggerWrapper在InputContainer中也占据所有可用空间
`

// 添加统一的标签样式，保证冒号对齐
const LabelSpan = styled.span.attrs({
  className: 'label-span'
})`
  display: inline-block;
  width: 80px;
  text-align: right;
  padding-right: 8px;
`

// 标题标签居中对齐
const CenterLabelSpan = styled.span`
  display: inline-block;
  width: 80px;
  text-align: center;
  padding-right: 8px;
`

// 输入区域容器样式，保证输入区域对齐
const InputContainer = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  width: 100%; // 确保占满可用宽度
`

// css-js end   ↑↑↑
class CommonOrderForm extends Component {

    // 默认展示所有审批选项，包含三级领导
    audit_schema = [
        { label: '部门领导', value: 'common' },
        { label: '两级领导', value: 'common_super_leader' },
        { label: '三级领导', value: 'common_three_level_leader' },
        { label: '指定审批人', value: 'common_pointed_approver' }
    ]
    ignore_new_sys_apply_template_txt = "新系统资源申请请参考模板 https://cheetahfun.feishu.cn/sheets/shtcnJgMCWDI98bh6SoB0mDVvBd，新建新的资源申请飞书文档。然后将文档的链接贴在该文本框内，并在文本框内写明申请理由。"
    new_sys_apply_template_txt =
        `新系统资源申请请参考模板 https://cheetahfun.feishu.cn/sheets/shtcnJgMCWDI98bh6SoB0mDVvBd，新建新的资源申请飞书文档。然后将文档的链接贴在该文本框内，并在文本框内写明申请理由。

申请理由：
新系统资源详情表(飞书文档链接)：`
    constructor(props) {
        super(props);
        
        // 预加载部门领导和二级领导数据，这些请求会被缓存
        this.leaderPromise = requestLeaderEmail({});
        this.superLeaderPromise = requestSuperLeaderEmails({});
    }
    
    state = {
        show_pointed_approver: false,
        pointed_approver: "",
        order_type: this.audit_schema[0].value,
        title: '',
        apply_msg: "",
        ops_audit_email: ["自动分配"],
        selected_ops_audit_email: "自动分配",
        leader_emails: undefined,
        dutySpectrumMemberMap: {},
        file_infos: [],
        showCcInput: false,
        ccList: []
    };
    
    componentDidMount() {
        // 使用已预加载的部门领导数据
        const leaderPromise = this.leaderPromise.then((data) => {
            this.setState({
                leader_emails: data.email,
            });
            return data;
        });
        
        const opsPromise = requestOpsMember({}).then((data) => {
            this.setState({
                ops_audit_email: [...this.state.ops_audit_email, ...data.members],
            });
            return data;
        });
        
        const dutyPromise = requestOpsMemberDutySpectrum({}).then((data) => {
            this.setState({
                dutySpectrumMemberMap: data.duty_member_map
            });
            return data;
        });
        
        // 等待数据加载完成
        Promise.all([this.superLeaderPromise, leaderPromise, opsPromise, dutyPromise])
            .catch(error => {
                console.error('加载数据时出错:', error);
            });
    }
    componentWillUnmount = () => {
        this.setState = (state, callback) => {
            return;
        };
    };
    handleTitleChange = (e) => {
        this.setState({
            title: e.target.value,
        });
    };
    handleApplyMsgChange = (e) => {
        this.setState({
            apply_msg: e.target.value,
        });
    };
    handleAuditEmailChange = (value) => {
        this.setState({
            selected_ops_audit_email: value,
        });
    };
    // 处理职责变更
    handleDutyChange = (value) => {
        if (value === "新系统上线资源申请") {
            this.setState({
                apply_msg: this.new_sys_apply_template_txt
            })
        }
        // 如果职责对应多人，则随机分配一人
        this.setState({
            selected_ops_audit_email: this.state.dutySpectrumMemberMap[value][Math.floor(Math.random() * this.state.dutySpectrumMemberMap[value].length)],
        });
    }
    // 文件上传
    handleFileUpload = (options) => {
        getFileContent(options.file).then((result) => {
            var wordArray = CryptoJS.lib.WordArray.create(result);
            var md5 = CryptoJS.MD5(wordArray).toString();
            // 获取日期
            let dateDir = moment().format('YYYY-MM-DD');
            let filePath = `commonOrderAttachment/${dateDir}/${md5}_${options.file.name}`
            // 获取腾讯云 cos 临时票据
            requestTxCamCosAuth({}).then((data) => {
                var COS = require('cos-js-sdk-v5');
                var cos = new COS({
                    SecurityToken: data.TmpToken,
                    SecretId: data.TmpSecretId,
                    SecretKey: data.TmpSecretKey
                });
                cos.putObject({
                    Bucket: 'ops-1252921383',   /* 必须 */
                    Region: 'ap-guangzhou',     /* 存储桶所在地域，必须字段 */
                    Key: filePath,              /* 必须 */
                    StorageClass: 'STANDARD',
                    Body: options.file, // 上传文件对象
                    onProgress: function (progressData) {
                        options.onProgress({ percent: progressData.percent * 100 })
                    }
                }, (err, data) => {
                    if (err === null) {
                        this.setState({
                            file_infos: [...this.state.file_infos, { "path": filePath, "name": options.file.name }]
                        })
                        options.onSuccess(data)
                        console.log({ "path": filePath, "file_name": options.file.name })
                    } else {
                        options.onError(err)
                    }
                });
            })
        })
    }
    // 处理抄送人变化
    handleCcChange = (newList) => {
        this.setState({ ccList: newList });
    };

    // 处理重置按钮
    resetHandle = () => {
        this.setState({
            order_type: this.audit_schema[0].value,
            title: '',
            apply_msg: "",
            selected_ops_audit_email: "自动分配",
            pointed_approver: "",
            ccList: [],
            showCcInput: false
        });
    };
    handleSubmit = () => {
        // 验证标题不能为空或仅包含空格
        const trimmedTitle = (this.state.title || '').trim();
        if (trimmedTitle.length <= 0) {
            message.warn("请输入有效标题", 1);
            return;
        }
        if (this.state.apply_msg.length <= 5) {
            message.warn("需求详叙述不能少于等于5个字", 1);
            return;
        }
        const apply_info = {
            title: this.state.title,
            ops_audit_email: this.state.selected_ops_audit_email,
        };
        if (this.state.order_type === this.audit_schema[3].value) {
            if (this.state.pointed_approver.length === 0) {
                message.warn("审批人员邮箱不能为空", 1);
                return;
            }
            apply_info.approver_email = this.state.pointed_approver
        }
        var apply_msg_filted = this.state.apply_msg
        apply_msg_filted = apply_msg_filted.replace(this.ignore_new_sys_apply_template_txt, '');
        var hasSpecialCharacters = /['"\t]/.test(apply_msg_filted);

        if (hasSpecialCharacters) {
            message.warn(`申请文本存在非法字符（"'\t），已自动去除，请知悉。`, 5);
            apply_msg_filted = apply_msg_filted.replace(/['"\t]/g, '');
        }
        // 构造抄送人信息
        const cc_user_infos = this.state.ccList.map(user => ({
            cc_open_id: user.open_id,
            cc_email: user.email
        }));

        const args = {
            apply_info: JSON.stringify(apply_info),
            order_type: this.state.order_type,
            title: this.state.title,
            apply_msg: apply_msg_filted,
            attachment_info: this.state.file_infos,
            cc_user_infos: cc_user_infos
        };
        requestCommonOrder(args).then((data) => {
            navigateModal(this.props.navigate, DoingOrderUrlSuffix, 3)
        });
    };
    handleOrderTypeChange = (value) => {
        this.setState({
            pointed_approver: "",
        });
        let show_pointed_approver = false;

        // 判断是否是指定审批人选项
        if (value === 'common_pointed_approver') {
            show_pointed_approver = true;
        } else {
            // 根据不同审批选项获取对应的领导邮箱
            if (value === 'common') { // 部门领导
                // 使用已缓存的部门领导数据
                this.leaderPromise.then((data) => {
                    this.setState({
                        leader_emails: data.email,
                    });
                });
            } else if (value === 'common_super_leader') { // 两级领导
                // 使用已缓存的二级领导数据
                this.superLeaderPromise.then((data) => {
                    this.setState({
                        leader_emails: data.emails.join(","),
                    });
                });
            } else if (value === 'common_three_level_leader') { // 三级领导
                // 直接在二级领导结果上拼接***************
                this.superLeaderPromise.then((data) => {
                    const threeLevelEmails = data.emails.join(",") + ",<EMAIL>";
                    this.setState({
                        leader_emails: threeLevelEmails,
                    });
                });
            }
        }

        this.setState({
            order_type: value,
            show_pointed_approver: show_pointed_approver
        })
    }
    render() {
        return (
            <SideContent>
                <CommonForm>
                    <OrderTitle>通用运维工单</OrderTitle>
                    {/* 业务类型和运维审批行 */}
                    <OrderRow>
                        <div style={{ display: 'flex', alignItems: 'center', marginRight: '20px' }}>
                            <LabelSpan>业务类型：</LabelSpan>
                            <Select
                                placeholder="请选择业务类型"
                                onChange={this.handleDutyChange}
                                style={{ width: '200px', height: '32px' }}
                            >
                                {
                                    Object.keys(this.state.dutySpectrumMemberMap).map((duty) => {
                                        return (
                                            <Option value={duty} key={duty}>
                                                {duty}
                                            </Option>
                                        );
                                    })}
                            </Select>
                        </div>
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                            <LabelSpan>运维审批：</LabelSpan>
                            <Select
                                placeholder="请选择运维审批人"
                                defaultValue={this.state.ops_audit_email[0]}
                                value={this.state.selected_ops_audit_email}
                                onChange={this.handleAuditEmailChange}
                                style={{ width: '200px', height: '32px' }}
                            >
                                {this.state.ops_audit_email.map((email) => {
                                    return (
                                        <Option value={email} key={email}>
                                            {email}
                                        </Option>
                                    );
                                })}
                            </Select>
                        </div>
                    </OrderRow>
                    {/* 审批模式行 */}
                        <OrderRow>
                        <LabelSpan>审批模式：</LabelSpan>
                        <InputContainer>
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                                <Segmented
                                    options={this.audit_schema}
                                    onChange={this.handleOrderTypeChange}
                                    value={this.state.order_type}
                                    style={{ minWidth: 'auto' }}
                                />
                            </div>
                        </InputContainer>
                    </OrderRow>
                    {/* 审批人员行 */}
                    <OrderRow>
                        <LabelSpan>审批人员：</LabelSpan>
                        <InputContainer>
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                                <Tag 
                                    color="#108ee9" 
                                    icon={<UserOutlined />} 
                                    className={this.state.show_pointed_approver ? "hide" : ""}
                                    style={{ maxWidth: '100%', overflow: 'hidden', textOverflow: 'ellipsis' }}
                                >
                                    {this.state.leader_emails}
                                </Tag>
                                <Input
                                    placeholder="请输入审批人邮箱"
                                    size="small"
                                    className={!this.state.show_pointed_approver ? "hide" : ""}
                                    value={this.state.pointed_approver}
                                    style={{ width: '100%' }}
                                    onChange={(e) => { this.setState({ pointed_approver: e.target.value }) }}
                                />
                            </div>
                        </InputContainer>
                    </OrderRow>
                    {/* 标题行 - 标题居中，冒号对齐 */}
                    <OrderRow>
                        <LabelSpan>标题：</LabelSpan>
                        <InputContainer>
                            <Input
                                value={this.state.title}
                                onChange={this.handleTitleChange}
                            />
                        </InputContainer>
                    </OrderRow>
                    {/* 需求描述行 */}
                    <OrderRow style={{ alignItems: "flex-start" }}>
                        <LabelSpan>需求描述：</LabelSpan>
                        <InputContainer>
                            <Input.TextArea
                                value={this.state.apply_msg}
                                onChange={this.handleApplyMsgChange}
                                style={{ minHeight: "15vw", width: '100%' }}
                            />
                        </InputContainer>
                    </OrderRow>
                    {/* 附件上传行 */}
                    <OrderRow style={{ alignItems: "flex-start" }}>
                        <LabelSpan>附件上传：</LabelSpan>
                        <InputContainer>
                            {/* 直接将Dragger放置在InputContainer中，并确保其宽度为100% */}
                            <Dragger
                                customRequest={this.handleFileUpload}
                                // onRemove={this.handleRemoveFile}
                                // beforeUpload={this.handleBeforeUpload}
                                style={{
                                    width: '100%',
                                    minHeight: "8vw",
                                    display: 'flex',
                                    flexDirection: 'column',
                                    justifyContent: 'center'
                                }} // 缩短高度并确保内容垂直居中
                            >
                                <p className="ant-upload-drag-icon">
                                    <InboxOutlined />
                                </p>
                                <p className="ant-upload-text">单击或拖动文件上传,审批时小文本可在线浏览，其他下载查看</p>
                            </Dragger>
                        </InputContainer>
                    </OrderRow>
                    {/* 抄送功能行 */}
                    <OrderRow style={{ alignItems: "flex-start" }}>
                        <LabelSpan>抄送：</LabelSpan>
                        <InputContainer>
                            <div style={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: '8px' }}>
                                <Button
                                    type="link"
                                    onClick={() => this.setState({ showCcInput: !this.state.showCcInput })}
                                    style={{ padding: 0, height: 'auto', marginLeft: 0 }}
                                >
                                    {this.state.showCcInput ? '收起' : '[添加抄送]'}
                                </Button>
                                {/* 当收起时且有抄送人员时显示已添加的抄送人员 */}
                                {!this.state.showCcInput && this.state.ccList.length > 0 && (
                                    <div style={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: '4px' }}>
                                        {this.state.ccList.map((user) => (
                                            <Tag
                                                key={user.email}
                                                color="blue"
                                                style={{ margin: 0 }}
                                            >
                                                {user.display_text}
                                            </Tag>
                                        ))}
                                    </div>
                                )}
                            </div>
                            {this.state.showCcInput && (
                                <div style={{ marginTop: '8px' }}>
                                    <CcInput
                                        value={this.state.ccList}
                                        onChange={this.handleCcChange}
                                        currentUserEmail={Cookies.get("user_email")}
                                    />
                                </div>
                            )}
                        </InputContainer>
                    </OrderRow>
                    <OrderSubmit>
                        <Button type="primary" onClick={this.handleSubmit}>
                            提交
                        </Button>
                        <Button type="dashed" onClick={this.resetHandle}>
                            重置
                        </Button>
                    </OrderSubmit>
                </CommonForm>
            </SideContent>
        );
    }
}

export default withRouter(CommonOrderForm)